using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Windows.Forms;
using uBuyFirst.Data;
using uBuyFirst.Purchasing;

namespace uBuyFirst.Tests.Purchasing
{
    [TestClass]
    public class BestOfferCheckoutTests
    {
        [TestMethod]
        public void IsValidGridControlForFlyout_WithNullControl_ReturnsFalse()
        {
            // Arrange
            Control nullControl = null;

            // Act
            var result = InvokeIsValidGridControlForFlyout(nullControl);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsValidGridControlForFlyout_WithControlNotInForm_ReturnsFalse()
        {
            // Arrange
            var control = new Control();
            var panel = new Panel();
            panel.Controls.Add(control);
            // Note: panel is not added to a Form

            // Act
            var result = InvokeIsValidGridControlForFlyout(control);

            // Assert
            Assert.IsFalse(result);
        }

        [TestMethod]
        public void IsValidGridControlForFlyout_WithControlInForm_ReturnsTrue()
        {
            // Arrange
            var form = new Form();
            var control = new Control();
            form.Controls.Add(control);

            // Act
            var result = InvokeIsValidGridControlForFlyout(control);

            // Assert
            Assert.IsTrue(result);

            // Cleanup
            form.Dispose();
        }

        [TestMethod]
        public void IsValidGridControlForFlyout_WithNestedControlInForm_ReturnsTrue()
        {
            // Arrange
            var form = new Form();
            var panel = new Panel();
            var control = new Control();
            
            form.Controls.Add(panel);
            panel.Controls.Add(control);

            // Act
            var result = InvokeIsValidGridControlForFlyout(control);

            // Assert
            Assert.IsTrue(result);

            // Cleanup
            form.Dispose();
        }

        /// <summary>
        /// Helper method to invoke the private IsValidGridControlForFlyout method using reflection
        /// </summary>
        private bool InvokeIsValidGridControlForFlyout(Control gridControl)
        {
            var method = typeof(BestOfferCheckout).GetMethod("IsValidGridControlForFlyout", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static);
            
            if (method == null)
                throw new InvalidOperationException("IsValidGridControlForFlyout method not found");

            return (bool)method.Invoke(null, new object[] { gridControl });
        }
    }
}
