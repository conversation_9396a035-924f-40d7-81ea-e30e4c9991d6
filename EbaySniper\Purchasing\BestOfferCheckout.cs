﻿using System;
using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Purchasing.Cookies; // Assuming CookieManager is here
using uBuyFirst.Stats;
using uBuyFirst.Pricing;

namespace uBuyFirst.Purchasing
{
    public static class BestOfferCheckout
    {
        /// <summary>
        /// Orchestrates the Best Offer submission process using HTTP emulation and cookies.
        /// </summary>
        /// <param name="d">The DataList item for the offer.</param>
        /// <param name="quantity">The quantity being offered.</param>
        /// <param name="offerTotalPrice">The price being offered.</param>
        /// <param name="itemPriceCurrency"></param>
        /// <param name="message">The optional message to the seller.</param>
        public static async Task ExecuteBestOfferSubmissionAsync(DataList d, int quantity, double offerTotalPrice, string offerCurrency, string? message)
        {
            var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
            flyoutSnackBar.ShowInformation(d.GridControl, "Best offer submission in progress");
            try
            {
                // Create a BestOfferOrder with the appropriate details
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                var bestOfferOrder = new BuyingService.BestOfferOrder(
                    d.ItemID,
                    d.Title,
                    d.EBaySite,
                    quantity,
                    effectivePurchasePrice,
                    offerTotalPrice,
                    offerCurrency,
                    message ?? string.Empty);
                bestOfferOrder.PurchaseProgress = new Progress<string>(msg => flyoutSnackBar.ShowInformation(d.GridControl, msg));

                // Set the order in DataList which will also set BestOfferOrder property
                d.Order = bestOfferOrder;
                d.SetStatus(ItemStatus.BestOfferInProgress); // Indicate process starting

                // 2. Read Cookies
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies started for Best Offer"); // Reuse logging
                // Load cookies for the specific domain from the order
                d.Order.CookieContainer = CookieManager.ReadCookiesFirefox(new[] { $".{d.Order.EbaySite.Domain}" });
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies stopped for Best Offer");
                if (d.Order.CookieContainer == null || d.Order.CookieContainer.Count == 0)
                {
                    d.Order.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed;
                    d.SetStatus(ItemStatus.Incorrect); // Or a more specific error status
                    return;
                }

                // 3. Call BestOfferService methods sequentially
                bool success;

                success = await BestOfferService._1_LoadMakeOfferPageAsync((BuyingService.BestOfferOrder)d.Order);
                if (!success) { HandleOfferFailure(d, "LoadMakeOfferPage"); return; }

                success = await BestOfferService._2_ReviewOfferAsync((BuyingService.BestOfferOrder)d.Order);
                if (!success) { HandleOfferFailure(d, "ReviewOffer"); return; }

                // --- Conditional Flow Logic ---
                var order = (BuyingService.BestOfferOrder)d.Order; // Cast once for convenience
                if (order.IsShortFlow)
                {
                    // Short Flow: Skip steps 3 & 4
                    PaymentLogger.LogPaymentToFile($"[{order.ItemID}] Short Best Offer flow detected. Proceeding to SendOffer.");
                    success = await BestOfferService._5_SendOfferAsync(order);
                    if (!success) { HandleOfferFailure(d, "SendOffer (Short Flow)"); return; }
                }
                else // Long Flow
                {
                    PaymentLogger.LogPaymentToFile($"[{order.ItemID}] Long Best Offer flow detected. Proceeding to SubmitFinalOffer.");
                    // Step 3: Submit Final Offer (Corrected method name)
                    success = await BestOfferService._3_PaymentReviewAsync(order);
                    if (!success) { HandleOfferFailure(d, "SubmitFinalOffer (Long Flow)"); return; }

                    // Step 4: Initialize Auth
                    success = await BestOfferService._4_InitAuthAsync(order);
                    if (!success) { HandleOfferFailure(d, "InitializeAuth (Long Flow)"); return; }

                    // Step 5: Send Offer Confirmation
                    success = await BestOfferService._5_SendOfferAsync(order);
                    if (!success) { HandleOfferFailure(d, "SendOffer (Long Flow)"); return; }
                }
                // --- End Conditional Flow Logic ---

                // 4. Final Status Update on Success (Only if the entire flow succeeded)
                d.Order.CurrentOfferStatus = BuyingService.OfferStatus.OfferSent;
                d.SetStatus(ItemStatus.BestOfferSubmitted); // Or a new Status like 'OfferSubmitted' if desired
                PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Best Offer submitted successfully (Flow: {(order.IsShortFlow ? "Short" : "Long")}).");
                flyoutSnackBar.ShowSuccess(d.GridControl, $"Best offer submitted successfully [{d.Title}]");
                var offerAmountUSD = CurrencyConverter.ConvertToUSD(offerTotalPrice, offerCurrency);
                Pixel.Track(Pixel.EventType.MakeOfferWeb, d.ItemID, offerAmountUSD);
            }
            catch (Exception ex)
            {
                PaymentLogger.LogPaymentToFile($"[{d.ItemID}] Exception during Best Offer submission: {ex}");
                HandleOfferFailure(d, "Exception");
            }
        }

        private static void HandleOfferFailure(DataList d, string stepName)
        {
            var failureReason = "Unknown error"; // Default message

            if (d.Order is BuyingService.BestOfferOrder bestOfferOrder) // Check if order exists and is the correct type
            {
                bestOfferOrder.CurrentOfferStatus = BuyingService.OfferStatus.OfferFailed; // Set status here
                if (!string.IsNullOrWhiteSpace(bestOfferOrder.FailureReasonMessage))
                {
                    failureReason = bestOfferOrder.FailureReasonMessage;
                }
            }

            var fullMessage = $"{failureReason}";
            var displayMessage = $"{fullMessage} [{d.Title}]";

            // Try to show UI notification if GridControl is valid, otherwise just log the message
            if (IsValidGridControlForFlyout(d.GridControl))
            {
                try
                {
                    var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                    flyoutSnackBar.ShowFailure(d.GridControl, displayMessage);
                }
                catch (Exception ex)
                {
                    // Fallback to logging if FlyoutPanel creation fails
                    PaymentLogger.LogPaymentToFile($"[{d.ItemID}] UI notification failed, logging instead: {displayMessage}. Error: {ex.Message}");
                }
            }

            PaymentLogger.LogPaymentToFile($"[{d.ItemID}] {fullMessage}");
            // d.Order status is set above if order exists
            // Consider setting a more specific error status if available
            d.SetStatus(ItemStatus.Incorrect);
        }

        /// <summary>
        /// Validates if a GridControl is properly contained within a Windows Form
        /// and can be used as a parent for FlyoutPanel
        /// </summary>
        private static bool IsValidGridControlForFlyout(System.Windows.Forms.Control gridControl)
        {
            if (gridControl == null)
                return false;

            // Walk up the parent chain to find a Form
            var current = gridControl;
            while (current != null)
            {
                if (current is System.Windows.Forms.Form)
                    return true;
                current = current.Parent;
            }

            return false;
        }
    }
}
