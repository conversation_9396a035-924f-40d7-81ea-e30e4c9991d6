﻿using System.Threading.Tasks;
using uBuyFirst.Data;
using uBuyFirst.GUI;
using uBuyFirst.Purchasing.Cookies;

namespace uBuyFirst.Purchasing
{
    public static class CreditCardCheckout
    {
        public static async Task ExecuteCreditCardCheckout(DataList d, int quantityToPurchase)
        {
            var quantity = quantityToPurchase;

            if (d.Order is not { OrderAction: Placeoffer.OrderAction.PayWithCreditCard })
            {
                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                d.Order = new BuyingService.BuyOrder(d.ItemID, d.Title, d.EBaySite, quantity, effectivePurchasePrice, Placeoffer.OrderAction.PayWithCreditCard);
            }

            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies started");
            d.Order.CookieContainer = CookieManager.ReadCookiesFirefox(new[] { $".{d.Order.EbaySite.Domain}" });
            PaymentLogger.LogPaymentToFile($"{d.Order.ItemID} Cookies stopped");
            switch (d.Order.CheckoutStatus)
            {
                case BuyingService.Order.CheckoutState.NotStarted:
                    // UI thread status updates - before and after async operations
                    d.SetStatus(ItemStatus.CreatingSession);

                    await CreditCardService.CreditCartCheckoutCompleteSequence(d);

                    // Update status based on operation result (still on UI thread)
                    UpdateStatusAfterCheckout(d);
                    ShowUserMessageAfterPurchaseAttempt(d);
                    break;

                case BuyingService.Order.CheckoutState.CreatingSession:
                    d.Order.AutoConfirmationAllowed = true;
                    break;

                case BuyingService.Order.CheckoutState.SessionCreated:
                    // UI thread status update
                    d.SetStatus(ItemStatus.PaymentInProgress);
                    d.Order.AutoConfirmationAllowed = true;

                    await CreditCardService.ConfirmCreditCardPayment((BuyingService.BuyOrder)d.Order, d.Title);

                    // Update status based on operation result (still on UI thread)
                    UpdateStatusAfterCheckout(d);
                    ShowUserMessageAfterPurchaseAttempt(d);
                    break;
            }
        }

        /// <summary>
        /// Updates the DataList status based on the checkout operation result
        /// This method runs on the UI thread after async operations complete
        /// </summary>
        private static void UpdateStatusAfterCheckout(DataList d)
        {
            var finalStatus = d.Order.CheckoutStatus switch
            {
                BuyingService.Order.CheckoutState.PaymentSuccess => ItemStatus.Active,
                BuyingService.Order.CheckoutState.TestPurchase => ItemStatus.TestPurchase,
                BuyingService.Order.CheckoutState.SessionCreationFailed => ItemStatus.Unknown,
                BuyingService.Order.CheckoutState.PaymentFailed => ItemStatus.Unknown,
                _ => ItemStatus.Unknown
            };

            d.SetStatus(finalStatus);
        }

        /// <summary>
        /// Shows user notification after purchase attempt
        /// Status updates are handled separately by UpdateStatusAfterCheckout
        /// </summary>
        private static void ShowUserMessageAfterPurchaseAttempt(DataList d)
        {
            // Check the final status on the order object after the confirmation completes
            if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.PaymentSuccess
                || d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
            {
                // Instantiate FlyoutPanelSnackBar directly
                string successMessage;
                if (d.Order.CheckoutStatus == BuyingService.Order.CheckoutState.TestPurchase)
                {
                    successMessage = $"Test purchase successful for {d.Title}.";
                    // Status update removed - handled by UpdateStatusAfterCheckout
                }
                else
                {
                    successMessage = $"Credit card payment successful for {d.Title}.";
                    // Status update removed - handled by UpdateStatusAfterCheckout
                }

                // Try to show UI notification if GridControl is valid, otherwise log the message
                if (IsValidGridControlForFlyout(d.GridControl))
                {
                    try
                    {
                        var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                        flyoutSnackBar.ShowSuccess(d.GridControl, successMessage);
                        // FlyoutPanelSnackBar handles its own disposal
                    }
                    catch (Exception ex)
                    {
                        // Fallback to logging if FlyoutPanel creation fails
                        PaymentLogger.LogPaymentToFile($"[{d.ItemID}] UI notification failed, logging instead: {successMessage}. Error: {ex.Message}");
                    }
                }
                else
                {
                    // Log the success message when UI notification is not available
                    PaymentLogger.LogPaymentToFile($"[{d.ItemID}] {successMessage}");
                }
            }
            else // Handle various failure states (PaymentFailed, ConfirmationFailed, etc.)
            {
                var failureMessage = d.Order.FailureReasonMessage ?? "Credit card payment failed (Unknown reason).";
                var fullFailureMessage = $"Credit card payment failed for {d.Title}: {failureMessage}";

                // Try to show UI notification if GridControl is valid, otherwise log the message
                if (IsValidGridControlForFlyout(d.GridControl))
                {
                    try
                    {
                        var flyoutSnackBar = new FlyoutPanelSnackBar(d.GridControl);
                        flyoutSnackBar.ShowFailure(d.GridControl, fullFailureMessage);
                        // FlyoutPanelSnackBar handles its own disposal
                    }
                    catch (Exception ex)
                    {
                        // Fallback to logging if FlyoutPanel creation fails
                        PaymentLogger.LogPaymentToFile($"[{d.ItemID}] UI notification failed, logging instead: {fullFailureMessage}. Error: {ex.Message}");
                    }
                }
                else
                {
                    // Log the failure message when UI notification is not available
                    PaymentLogger.LogPaymentToFile($"[{d.ItemID}] {fullFailureMessage}");
                }
            }
        }

        /// <summary>
        /// Validates if a GridControl is properly contained within a Windows Form
        /// and can be used as a parent for FlyoutPanel
        /// </summary>
        private static bool IsValidGridControlForFlyout(System.Windows.Forms.Control gridControl)
        {
            if (gridControl == null)
                return false;

            // Walk up the parent chain to find a Form
            var current = gridControl;
            while (current != null)
            {
                if (current is System.Windows.Forms.Form)
                    return true;
                current = current.Parent;
            }

            return false;
        }
    }
}
